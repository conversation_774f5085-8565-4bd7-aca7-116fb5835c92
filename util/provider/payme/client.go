package payme

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/bytedance/sonic"
)

var json = sonic.ConfigDefault

const defaultTimeout = 5 * time.Second

type Client struct {
	id             string
	idAndPassword  string
	baseUrl        string
	httpClient     http.Client
	httpClientLong http.Client
}

func NewClient(baseUrl, paymeId, paymeKey string) *Client {
	return &Client{
		baseUrl:        baseUrl,
		id:             paymeId,
		idAndPassword:  paymeId + ":" + paymeKey,
		httpClient:     http.Client{Timeout: defaultTimeout},
		httpClientLong: http.Client{Timeout: 60 * time.Second},
	}
}

func (s *Client) sendRequest(ctx context.Context, request Request, withId bool) (resp Response, err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.baseUrl, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	if withId {
		req.Header.Set("X-Auth", s.id)
	} else {
		req.Header.Set("X-Auth", s.idAndPassword)
	}

	req.Header.Set("Content-Type", "application/json")

	response, err := s.httpClient.Do(req)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			err = ErrTimeoutExceeded
		}
		return
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		var body []byte
		body, err = io.ReadAll(response.Body)
		if err != nil {
			err = fmt.Errorf("response not ok: %s, read body error: %v", response.Status, err)
			return
		}
		_ = json.Unmarshal(body, &resp)
		if resp.Error != nil {
			err = extractError(resp.Error)
			if err != nil && err != ErrUnknown {
				return
			}
		}
		err = fmt.Errorf("response not ok: %s, body: %s", response.Status, string(body))
		return
	}

	err = json.NewDecoder(response.Body).Decode(&resp)
	if err != nil {
		return
	}

	if resp.Error != nil {
		err = extractError(resp.Error)
	}

	return
}

func (s *Client) sendRequestLong(ctx context.Context, request Request, withId bool) (resp Response, err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.baseUrl, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	if withId {
		req.Header.Set("X-Auth", s.id)
	} else {
		req.Header.Set("X-Auth", s.idAndPassword)
	}
	req.Header.Set("Content-Type", "application/json")

	response, err := s.httpClientLong.Do(req)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			err = ErrTimeoutExceeded
		}
		return
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		var body []byte
		body, err = io.ReadAll(response.Body)
		if err != nil {
			err = fmt.Errorf("response not ok: %s, read body error: %v", response.Status, err)
			return
		}
		_ = json.Unmarshal(body, &resp)
		if resp.Error != nil {
			err = extractError(resp.Error)
			if err != nil && err != ErrUnknown {
				return
			}
		}
		err = fmt.Errorf("response not ok: %s, body: %s", response.Status, string(body))
		return
	}

	err = json.NewDecoder(response.Body).Decode(&resp)

	if resp.Error != nil {
		err = extractError(resp.Error)
		if err == ErrUnknown {
			fmt.Printf("payme error response: %s code: %d data: %v\n", resp.Error.Message, resp.Error.Code, resp.Error.Data)
		}
	}

	return
}
