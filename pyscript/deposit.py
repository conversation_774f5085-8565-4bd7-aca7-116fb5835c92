import requests
import xml.etree.ElementTree as ET
import time
import pandas as pd

# Load Excel
df = pd.read_excel("./Тест_Водители_с_балансом_24.07.2025.xlsx")

# API Constants
URL = "http://185.100.53.100:88"
USERNAME = "MytaxiAPIClick2015"
PASSWORD = "6dd21114327bf55c16af905fe0acff845acce3e5fb39f72aab25a40a31bb355c"
ID_ACCOUNT = 563  # Account ID for deposits
TARGET_BALANCE = 100  # Balance should be brought up to this value

# Headers
headers_get = {"Content-Type": "application/xml"}
headers_deposit = {
    "Content-Type": "application/xml"
}

for i, row in df.iterrows():
    driver_id = row["driver_id"]

    # Step 1: Get current balance
    balance_request = f"""
    <ROOT>
        <action>get_acc_bal_driver</action>
        <username>{USERNAME}</username>
        <password>{PASSWORD}</password>
        <driver_id>{driver_id}</driver_id>
    </ROOT>
    """.strip()

    try:
        response = requests.post(URL, headers=headers_get, data=balance_request.encode("utf-8"))
        response.raise_for_status()
        xml = ET.fromstring(response.content)
        current_bal = float(xml.findtext("bal", default="0"))
    except Exception as e:
        print(f"\n❌ Error fetching balance for driver {driver_id}: {e}")
        continue

    print(f"\n👤 Driver {driver_id}: current balance = {current_bal}")

    # Step 2: Deposit if needed
    if current_bal < TARGET_BALANCE:
        to_deposit = round(TARGET_BALANCE - current_bal)
        print(f"→ Need to deposit: {to_deposit}")

        # Create unique mytaxi_id using current Unix time
        mytaxi_id = str(int(time.time()))

        deposit_request = f"""<?xml version="1.0" encoding="UTF-8"?>
        <ROOT>
            <action>driver_deposit</action>
            <mytaxi_id>{mytaxi_id}</mytaxi_id>
            <cash>2</cash>
            <username>{USERNAME}</username>
            <password>{PASSWORD}</password>
            <driver_id>{driver_id}</driver_id>
            <amount>{to_deposit}</amount>
            <comment>Автоматическое пополнение до -5000</comment>
            <id_account>{ID_ACCOUNT}</id_account>
        </ROOT>
        """.strip()

        try:
            deposit_response = requests.post(URL, headers=headers_deposit, data=deposit_request.encode("utf-8"))
            deposit_response.raise_for_status()
            deposit_xml = ET.fromstring(deposit_response.content)
            success = deposit_xml.findtext("success")

            balance_text = deposit_xml.findtext("balance", default=None)
            if balance_text is None or balance_text.strip() == "":
                print("⚠️ No balance returned after deposit.")
                new_bal = "Unknown"
            else:
                new_bal = float(balance_text)

            print(f"✅ Deposited successfully: success={success}")
            print(f"📈 New balance: {new_bal}")
        except Exception as e:
            print(f"❌ Error depositing for driver {driver_id}: {e}")
            print("↪️ Response content:")
            print(deposit_response.content.decode("utf-8"))
    else:
        print("⏭ No deposit needed (balance already at or above threshold).")

    time.sleep(1)
