import requests
import xml.etree.ElementTree as ET
import time
import pandas as pd

# Load Excel
df = pd.read_excel("./Тест_Водители_с_балансом_24.07.2025.xlsx")

# API Constants
URL = "http://185.100.53.100:88"
USERNAME = "MytaxiAPIClick2015"
PASSWORD = "6dd21114327bf55c16af905fe0acff845acce3e5fb39f72aab25a40a31bb355c"
ACCOUNT_ID = 11583
TARGET_BALANCE = -5000  # Balance must be ≤ this value

# Headers
headers_get = {"Content-Type": "application/xml"}
headers_deduct = {
    "Content-Type": "application/xml",
    "APP-LANGUAGE": "RU",
    "APP-VERSION": "12"
}

for i, row in df.iterrows():
    driver_id = row["driver_id"]

    # Step 1: Get current balance
    balance_request = f"""
    <ROOT>
        <action>get_acc_bal_driver</action>
        <username>{USERNAME}</username>
        <password>{PASSWORD}</password>
        <driver_id>{driver_id}</driver_id>
    </ROOT>
    """.strip()

    try:
        response = requests.post(URL, headers=headers_get, data=balance_request.encode("utf-8"))
        response.raise_for_status()
        xml = ET.fromstring(response.content)
        current_bal = float(xml.findtext("bal", default="0"))
    except Exception as e:
        print(f"\n❌ Error fetching balance for driver {driver_id}: {e}")
        continue

    print(f"\n👤 Driver {driver_id}: current balance = {current_bal}")

    # Step 2: Deduct if needed
    if current_bal > TARGET_BALANCE:
        to_deduct = round(current_bal - TARGET_BALANCE)
        print(f"→ Need to deduct: {to_deduct}")

        # Create unique mytaxi_id using current Unix time
        mytaxi_id = str(int(time.time()))

        deduct_request = f"""
        <ROOT>
            <action>driver_fine</action>
            <mytaxi_id>{mytaxi_id}</mytaxi_id>
            <username>{USERNAME}</username>
            <password>{PASSWORD}</password>
            <driver_id>{driver_id}</driver_id>
            <amount>{to_deduct}</amount>
            <comment>Автоматическое списание до -5000</comment>
            <id_account_to>{ACCOUNT_ID}</id_account_to>
        </ROOT>
        """.strip()

        try:
            deduct_response = requests.post(URL, headers=headers_deduct, data=deduct_request.encode("utf-8"))
            deduct_response.raise_for_status()
            deduct_xml = ET.fromstring(deduct_response.content)
            success = deduct_xml.findtext("success")

            balance_text = deduct_xml.findtext("balance", default=None)
            if balance_text is None or balance_text.strip() == "":
                print("⚠️ No balance returned after deduction.")
                new_bal = "Unknown"
            else:
                new_bal = float(balance_text)

            print(f"✅ Deducted successfully: success={success}")
            print(f"📉 New balance: {new_bal}")
        except Exception as e:
            print(f"❌ Error deducting for driver {driver_id}: {e}")
            print("↪️ Response content:")
            print(deduct_response.content.decode("utf-8"))
    else:
        print("⏭ No deduction needed (balance already below threshold).")

    time.sleep(1)
