package app

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"

	"billing_service/model"
	"billing_service/util"
	"billing_service/util/provider/atmos"

	null "github.com/guregu/null/v6"
)

func (a *App) AtmosWebhook(ctx context.Context, req atmos.WebhookRequest) (err error) {
	if !a.verifyAtmosSignature(req) {
		return errors.New("invalid signature")
	}

	// Atmos faqat card add uchun webhook yuboradi
	if req.CardID == "" {
		return errors.New("card_id is required")
	}

	return a.AtmosWebhookCardAdd(ctx, req)
}

func (a *App) verifyAtmosSignature(req atmos.WebhookRequest) bool {
	// Formula: store_id+transaction_id+invoice+amount+api_key without separators
	apiKey := a.cfg.Atmos.ApiKey

	dataToHash := req.StoreID + req.TransactionID + req.Invoice.String() + "" + apiKey

	h := md5.New()
	h.Write([]byte(dataToHash))
	calculatedHash := fmt.Sprintf("%x", h.Sum(nil))

	return calculatedHash == req.Sign
}

func (a *App) AtmosWebhookCardAdd(ctx context.Context, req atmos.WebhookRequest) (err error) {
	userId := util.ParseInt(req.Invoice.String())

	// Get card details with hash (unique_card_token)
	cardDetailsWithHash, err := a.atmos.GetCardDetailsWithHash(ctx, req.CardID)
	if err != nil {
		a.log.Errorf("Failed to get card details with hash: %v", err)
		return fmt.Errorf("add atmos card: %v", err)
	}

	if !cardDetailsWithHash.Payload.Status {
		a.log.Errorf("invalid card: %s", cardDetailsWithHash.Status.Message)
		return fmt.Errorf("add atmos card: %s", cardDetailsWithHash.Status.Message)
	}

	brand, category, err := a.getCardBrand(ctx, cardDetailsWithHash.Payload.MaskedPan)
	if err != nil {
		return fmt.Errorf("add atmos card: %v", err)
	}

	// Format the masked PAN to ensure it's not longer than 16 characters
	maskedPan := atmos.FormatMaskedPAN(cardDetailsWithHash.Payload.MaskedPan)

	// Use the unique_card_token as atmos_hash
	card := model.Card{
		Number:     maskedPan,
		Brand:      brand,
		Category:   category,
		AtmosToken: null.StringFrom(req.CardID),
		AtmosHash:  null.StringFrom(cardDetailsWithHash.Payload.UniqueCardToken),
	}

	err = a.repo.AddAtmosCard(ctx, userId, "client", card)
	if err != nil {
		return fmt.Errorf("add atmos card: %v", err)
	}
	return
}
